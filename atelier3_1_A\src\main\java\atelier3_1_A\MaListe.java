package atelier3_1_A;

public class MaListe<C extends Comparable<C>> implements Liste<C> {
    private C[] valeurs;

    public MaListe(C[] valeursInitiales) {
        this.valeurs = valeursInitiales;
    }

    @Override
    public C obtenirValeur(int indice) {
        return valeurs[indice];
    }

    @Override
    public void modifierValeur(int indice, C valeur) {
        valeurs[indice] = valeur;
    }

    @Override
    public C valeurMinimale() {
        C min = valeurs[0];
        for (int i = 1; i < valeurs.length; i++) {
            if (valeurs[i].compareTo(min) < 0) {
                min = valeurs[i];
            }
        }
        return min;
    }
}
